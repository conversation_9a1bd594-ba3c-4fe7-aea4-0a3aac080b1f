import type {
  GroupType,
  PoolType,
  StakeholderType,
  VestingFrequency,
} from "@prisma/client";
import { db } from "~/server/db";
import type { ProjectData } from "~/server/schemas/project-data";

// Simple enum mapping functions
const mapToPoolType = (allocationName: string): PoolType => {
  const name = allocationName.toLowerCase();
  if (name.includes("team") || name.includes("employee")) return "Employees";
  if (name.includes("founder")) return "Founders";
  if (name.includes("advisor")) return "Advisors";
  if (name.includes("investor")) return "Investors";
  if (name.includes("community")) return "Community";
  if (name.includes("treasury")) return "Treasury";
  return "Other";
};

const mapToStakeholderType = (allocationName: string): StakeholderType => {
  const name = allocationName.toLowerCase();
  if (name.includes("team") || name.includes("employee")) return "Employee";
  if (name.includes("founder")) return "Founder";
  if (name.includes("advisor")) return "Advisor";
  if (name.includes("investor")) return "Investor";
  if (name.includes("consultant")) return "Consultant";
  return "Other";
};

const mapToGroupType = (allocationName: string): GroupType => {
  const name = allocationName.toLowerCase();
  if (name.includes("team")) return "Team";
  if (name.includes("founder")) return "Founders";
  if (name.includes("advisor")) return "Advisors";
  if (name.includes("series a")) return "SeriesA";
  if (name.includes("series b")) return "SeriesB";
  if (name.includes("common")) return "Common";
  return "Other";
};

const mapToVestingFrequency = (
  frequencyType: string | null
): VestingFrequency => {
  if (!frequencyType) return "ONE_TIME";
  switch (frequencyType.toLowerCase()) {
    case "day": {
      return "DAILY";
    }
    case "week": {
      return "WEEKLY";
    }
    case "month": {
      return "MONTHLY";
    }
    case "year": {
      return "ANNUALLY";
    }
    default: {
      return "ONE_TIME";
    }
  }
};

// Calculate cliff duration from batch data
const calculateCliffDuration = (
  allocation: ProjectData["data"]["allocations"][0]
): string => {
  if (!allocation.batches || allocation.batches.length === 0) {
    return "P0D";
  }

  const tgeBatch = allocation.batches.find((batch) => batch.is_tge);
  if (!tgeBatch) {
    return "P0D";
  }

  const nonTgeBatches = allocation.batches
    .filter((batch) => !batch.is_tge)
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

  if (nonTgeBatches.length === 0) {
    return "P0D";
  }

  const firstNonTgeBatch = nonTgeBatches[0]!;
  const tgeDate = new Date(tgeBatch.date);
  const firstUnlockDate = new Date(firstNonTgeBatch.date);

  const diffMs = firstUnlockDate.getTime() - tgeDate.getTime();
  const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (days <= 0) return "P0D";
  if (days >= 365) return `P${Math.floor(days / 365)}Y`;
  if (days >= 30) return `P${Math.floor(days / 30)}M`;
  return `P${days}D`;
};

// Note: For public project data, we only create unlock schedules since vesting schedules
// are private employee-specific information not available in public data

// Main import function
export const importPublicProjectData = async (
  projectData: ProjectData,
  organizationId: string,
  uploadedBy: string,
  fileName: string,
  projectName: string,
  projectSymbol: string
) => {
  console.log("🚀 Starting project data import transaction...");

  return await db.$transaction(
    async (tx) => {
      console.log("📊 Step 1: Creating project entry...");
      // Step 1: Create Project Entry
      const totalTokenSupply = projectData.data.allocations.reduce(
        (sum, allocation) => sum + allocation.tokens,
        0
      );

      const project = await tx.project.create({
        data: {
          organizationId,
          name: projectName, // Use provided project name
          symbol: projectSymbol, // Use provided project symbol
          totalTokenSupply: BigInt(totalTokenSupply),
          coinId: projectData.data.vesting.coin_id,
          tgeStartDate: new Date(projectData.data.vesting.tge_start_date),
          totalStartDate: new Date(projectData.data.vesting.total_start_date),
          // Skip insider/outsider calculations as requested
          insiderAllocationPoolDecimal: 0,
          outsiderAllocationPoolDecimal: 0,
        },
      });

      console.log(
        `Created project: "${projectName}" (${projectSymbol}) - ID: ${project.id} for coin ${projectData.data.vesting.coin_id}`
      );

      // Step 2: Create AllocationPool entries using createManyAndReturn for better performance
      const allocationPoolsData = projectData.data.allocations.map(
        (allocation) => {
          const poolType = mapToPoolType(allocation.name);
          const allocationDecimal = allocation.tokens_percent / 100;

          return {
            projectId: project.id,
            name: allocation.name,
            description: `${allocation.name} allocation pool`,
            poolType,
            allocationDecimal,
          };
        }
      );

      const allocationPools = await tx.allocationPool.createManyAndReturn({
        data: allocationPoolsData,
      });

      console.log(`Created ${allocationPools.length} allocation pools`);

      // Step 3: Create Allocation entries
      const allocations = await Promise.all(
        projectData.data.allocations.map(async (allocation, index) => {
          const stakeholderType = mapToStakeholderType(allocation.name);
          const groupType = mapToGroupType(allocation.name);
          const tokenDecimal = allocation.tokens_percent / 100;

          return await tx.allocation.create({
            data: {
              projectId: project.id,
              categoryId: allocationPools[index]!.id,
              stakeholderType,
              group: groupType,
              tokenAmount: allocation.tokens,
              totalTokenDecimal: tokenDecimal,
              isGenerated: true,
            },
          });
        })
      );

      console.log(`Created ${allocations.length} allocations`);

      // Step 4: Create UnlockSchedules (public data contains unlock info, not vesting info)
      console.log("Creating unlock schedules from public allocation data...");
      const unlockSchedules = [];

      for (
        let index = 0;
        index < projectData.data.allocations.length;
        index++
      ) {
        const allocation = projectData.data.allocations[index]!;

        // Only create unlock schedules for allocations that have unlock data
        if (allocation.batches.length > 0) {
          const tgeBatch = allocation.batches.find((batch) => batch.is_tge);
          const unlockFrequency = mapToVestingFrequency(
            allocation.unlock_frequency_type
          );

          // Calculate lockup durations from the allocation data
          const cliffDuration = calculateCliffDuration(allocation);
          let totalDuration = "P0D";

          if (
            allocation.vesting_duration_type &&
            allocation.vesting_duration_value
          ) {
            switch (allocation.vesting_duration_type) {
              case "day": {
                totalDuration = `P${allocation.vesting_duration_value}D`;
                break;
              }
              case "week": {
                totalDuration = `P${allocation.vesting_duration_value * 7}D`;
                break;
              }
              case "month": {
                totalDuration = `P${allocation.vesting_duration_value}M`;
                break;
              }
              case "year": {
                totalDuration = `P${allocation.vesting_duration_value}Y`;
                break;
              }
            }
          }

          const unlockSchedule = await tx.unlockSchedule.create({
            data: {
              projectId: project.id,
              name: `${allocation.name} Unlock Schedule`,
              description: `Public unlock schedule for ${allocation.name} allocation`,
              lockupCliffDuration: cliffDuration,
              lockupTotalDuration: totalDuration,
              tgeDate: tgeBatch ? new Date(tgeBatch.date) : null,
              tgeUnlockDecimal: tgeBatch ? tgeBatch.unlock_percent / 100 : 0,
              unlockFrequency,
              unlockDecimalPerPeriod:
                allocation.unlock_type === "linear" &&
                allocation.unlock_frequency_value
                  ? 1 /
                    (allocation.vesting_duration_value || 1) /
                    (allocation.unlock_frequency_value || 1)
                  : null,
            },
          });

          // Link allocation to unlock schedule
          await tx.allocation.update({
            where: { allocationId: allocations[index]!.allocationId },
            data: {
              unlockSchedules: {
                connect: { id: unlockSchedule.id },
              },
            },
          });

          unlockSchedules.push(unlockSchedule);
          console.log(
            `Created unlock schedule for ${allocation.name} with ${allocation.batches.length} unlock events`
          );
        }
      }

      console.log(`Created ${unlockSchedules.length} unlock schedules`);

      return {
        project,
        allocationPools,
        allocations,
        unlockSchedules,
        summary: {
          projectId: project.id,
          coinId: projectData.data.vesting.coin_id,
          totalAllocations: allocations.length,
          totalTokenSupply,
          fileName,
          uploadedBy,
        },
      };
    },
    {
      timeout: 60_000, // 60 seconds timeout
    }
  );
};
